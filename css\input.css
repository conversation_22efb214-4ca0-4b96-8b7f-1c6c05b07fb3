/* Tailwind CSS is loaded via CDN */

/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom styles for Arabic text */
.arabic-text {
  font-family: 'Amiri', 'Scheherazade New', serif;
  direction: rtl;
  text-align: right;
  line-height: 2;
  font-size: 1.5rem;
}

.verse-highlight {
  @apply bg-islamic-gold bg-opacity-20 rounded-md px-2 py-1 transition-all duration-300;
}

.verse-highlight:hover {
  @apply bg-islamic-gold bg-opacity-40 cursor-pointer;
}

/* Audio player custom styles */
.audio-controls {
  @apply flex items-center space-x-4 bg-white rounded-lg shadow-md p-4;
}

.play-button {
  @apply bg-islamic-green text-white rounded-full p-3 hover:bg-opacity-80 transition-all duration-200;
}

/* Progress bar */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply bg-islamic-green h-2 rounded-full transition-all duration-300;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .arabic-text {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}
