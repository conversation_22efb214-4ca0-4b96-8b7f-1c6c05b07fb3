/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom styles for Arabic text */
.arabic-text {
  font-family: 'Amiri', 'Scheherazade New', serif;
  direction: rtl;
  text-align: right;
  line-height: 2;
  font-size: 1.5rem;
}

.verse-highlight {
  background-color: rgba(255, 215, 0, 0.2);
  border-radius: 0.375rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.verse-highlight:hover {
  background-color: rgba(255, 215, 0, 0.4);
  cursor: pointer;
}

/* Audio player custom styles */
.audio-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.play-button {
  background-color: #0D7377;
  color: white;
  border-radius: 50%;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.play-button:hover {
  background-color: rgba(13, 115, 119, 0.8);
}

/* Progress bar */
.progress-bar {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  height: 0.5rem;
}

.progress-fill {
  background-color: #0D7377;
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

/* Islamic color scheme */
.bg-islamic-green {
  background-color: #0D7377;
}

.bg-islamic-gold {
  background-color: #FFD700;
}

.bg-islamic-cream {
  background-color: #F5F5DC;
}

.bg-islamic-dark {
  background-color: #2C3E50;
}

.bg-islamic-light {
  background-color: #ECF0F1;
}

.text-islamic-green {
  color: #0D7377;
}

.text-islamic-gold {
  color: #FFD700;
}

.text-islamic-cream {
  color: #F5F5DC;
}

.text-islamic-dark {
  color: #2C3E50;
}

.text-islamic-light {
  color: #ECF0F1;
}

.border-islamic-green {
  border-color: #0D7377;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .arabic-text {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-shadow:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0D7377;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0a5d61;
}
