/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Custom styles for Arabic text */
.arabic-text {
  font-family: 'Amiri', serif;
  direction: rtl;
  text-align: right;
  line-height: 2;
  font-size: 1.5rem;
}

.verse-highlight {
  background-color: rgba(222, 174, 53, 0.2);
  border-radius: 0.375rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.verse-highlight:hover {
  background-color: rgba(222, 174, 53, 0.4);
  cursor: pointer;
}

/* Audio player custom styles */
.audio-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.play-button {
  background-color: #deae35;
  color: #101010;
  border-radius: 50%;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.play-button:hover {
  background-color: rgba(222, 174, 53, 0.8);
}

/* Progress bar */
.progress-bar {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  height: 0.5rem;
}

.progress-fill {
  background-color: #deae35;
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .arabic-text {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-shadow:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #deae35;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c49a2e;
}
