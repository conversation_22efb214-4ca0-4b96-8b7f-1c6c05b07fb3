// Lesson 1: Arabic Alphabet
class ArabicAlphabetLesson {
    constructor() {
        this.arabicLetters = [
            {
                id: 1,
                letter: 'ا',
                name: '<PERSON><PERSON>',
                transliteration: '<PERSON><PERSON>',
                sound: 'A - Arm',
                audioFile: '../assets/audio/alif.m4a',
                forms: {
                    isolated: 'ا',
                    beginning: 'ا',
                    middle: 'ـا',
                    end: 'ـا'
                },
                examples: [
                    { arabic: 'أب', transliteration: 'ab', meaning: 'father' },
                    { arabic: 'أم', transliteration: 'umm', meaning: 'mother' }
                ],
                learned: false
            },
            {
                id: 2,
                letter: 'ب',
                name: 'Baa',
                transliteration: 'Baa',
                sound: 'Ba - Ball',
                forms: {
                    isolated: 'ب',
                    beginning: 'بـ',
                    middle: 'ـبـ',
                    end: 'ـب'
                },
                examples: [
                    { arabic: 'بيت', transliteration: 'bayt', meaning: 'house' },
                    { arabic: 'كتاب', transliteration: 'kitab', meaning: 'book' }
                ],
                learned: false
            },
            {
                id: 3,
                letter: 'ت',
                name: '<PERSON><PERSON>',
                transliteration: 'Ta<PERSON>',
                sound: 'Ta - Task',
                forms: {
                    isolated: 'ت',
                    beginning: 'تـ',
                    middle: 'ـتـ',
                    end: 'ـت'
                },
                examples: [
                    { arabic: 'تفاح', transliteration: 'tuffah', meaning: 'apple' },
                    { arabic: 'بنت', transliteration: 'bint', meaning: 'girl' }
                ],
                learned: false
            },
            {
                id: 4,
                letter: 'ث',
                name: 'Thaa',
                transliteration: 'Thaa',
                sound: 'Tha - That',
                forms: {
                    isolated: 'ث',
                    beginning: 'ثـ',
                    middle: 'ـثـ',
                    end: 'ـث'
                },
                examples: [
                    { arabic: 'ثلاثة', transliteration: 'thalatha', meaning: 'three' },
                    { arabic: 'ثوب', transliteration: 'thawb', meaning: 'garment' }
                ],
                learned: false
            },
            {
                id: 5,
                letter: 'ج',
                name: 'Jeem',
                transliteration: 'Jeem',
                sound: 'Je - Jet',
                forms: {
                    isolated: 'ج',
                    beginning: 'جـ',
                    middle: 'ـجـ',
                    end: 'ـج'
                },
                examples: [
                    { arabic: 'جمل', transliteration: 'jamal', meaning: 'camel' },
                    { arabic: 'مسجد', transliteration: 'masjid', meaning: 'mosque' }
                ],
                learned: false
            },
            {
                id: 6,
                letter: 'ح',
                name: 'Haa',
                transliteration: 'Haa',
                sound: 'Ha - Hand, Hat',
                forms: {
                    isolated: 'ح',
                    beginning: 'حـ',
                    middle: 'ـحـ',
                    end: 'ـح'
                },
                examples: [
                    { arabic: 'حمد', transliteration: 'hamd', meaning: 'praise' },
                    { arabic: 'رحمة', transliteration: 'rahma', meaning: 'mercy' }
                ],
                learned: false
            },
            {
                id: 7,
                letter: 'خ',
                name: 'Khaa',
                transliteration: 'Khaa',
                sound: 'Kha - Khaki',
                forms: {
                    isolated: 'خ',
                    beginning: 'خـ',
                    middle: 'ـخـ',
                    end: 'ـخ'
                },
                examples: [
                    { arabic: 'خير', transliteration: 'khayr', meaning: 'good' },
                    { arabic: 'أخ', transliteration: 'akh', meaning: 'brother' }
                ],
                learned: false
            },
            {
                id: 8,
                letter: 'د',
                name: 'Dal',
                transliteration: 'Dal',
                sound: 'Da - Dab, Dual',
                forms: {
                    isolated: 'د',
                    beginning: 'د',
                    middle: 'ـد',
                    end: 'ـد'
                },
                examples: [
                    { arabic: 'دار', transliteration: 'dar', meaning: 'house' },
                    { arabic: 'يد', transliteration: 'yad', meaning: 'hand' }
                ],
                learned: false
            },
            {
                id: 9,
                letter: 'ذ',
                name: 'Thal',
                transliteration: 'Thal',
                sound: 'Zha - Zhan',
                forms: {
                    isolated: 'ذ',
                    beginning: 'ذ',
                    middle: 'ـذ',
                    end: 'ـذ'
                },
                examples: [
                    { arabic: 'ذهب', transliteration: 'dhahab', meaning: 'gold' },
                    { arabic: 'أذن', transliteration: 'udhn', meaning: 'ear' }
                ],
                learned: false
            },
            {
                id: 10,
                letter: 'ر',
                name: 'Raa',
                transliteration: 'Raa',
                sound: 'Ra - Run',
                forms: {
                    isolated: 'ر',
                    beginning: 'ر',
                    middle: 'ـر',
                    end: 'ـر'
                },
                examples: [
                    { arabic: 'رب', transliteration: 'rabb', meaning: 'Lord' },
                    { arabic: 'نور', transliteration: 'nur', meaning: 'light' }
                ],
                learned: false
            },
            {
                id: 11,
                letter: 'ز',
                name: 'Zaay',
                transliteration: 'Zaay',
                sound: 'Za - Zag',
                forms: {
                    isolated: 'ز',
                    beginning: 'ز',
                    middle: 'ـز',
                    end: 'ـز'
                },
                examples: [
                    { arabic: 'زهرة', transliteration: 'zahra', meaning: 'flower' },
                    { arabic: 'عزيز', transliteration: 'aziz', meaning: 'dear' }
                ],
                learned: false
            },
            {
                id: 12,
                letter: 'س',
                name: 'Seen',
                transliteration: 'Seen',
                sound: 'Se - Sell',
                forms: {
                    isolated: 'س',
                    beginning: 'سـ',
                    middle: 'ـسـ',
                    end: 'ـس'
                },
                examples: [
                    { arabic: 'سلام', transliteration: 'salam', meaning: 'peace' },
                    { arabic: 'مدرسة', transliteration: 'madrasa', meaning: 'school' }
                ],
                learned: false
            },
            {
                id: 13,
                letter: 'ش',
                name: 'Sheen',
                transliteration: 'Sheen',
                sound: 'She - Shell',
                forms: {
                    isolated: 'ش',
                    beginning: 'شـ',
                    middle: 'ـشـ',
                    end: 'ـش'
                },
                examples: [
                    { arabic: 'شمس', transliteration: 'shams', meaning: 'sun' },
                    { arabic: 'عيش', transliteration: 'aysh', meaning: 'bread' }
                ],
                learned: false
            },
            {
                id: 14,
                letter: 'ص',
                name: 'Saad',
                transliteration: 'Saad',
                sound: 'Saa - Swab',
                forms: {
                    isolated: 'ص',
                    beginning: 'صـ',
                    middle: 'ـصـ',
                    end: 'ـص'
                },
                examples: [
                    { arabic: 'صلاة', transliteration: 'salah', meaning: 'prayer' },
                    { arabic: 'قصة', transliteration: 'qissa', meaning: 'story' }
                ],
                learned: false
            },
            {
                id: 15,
                letter: 'ض',
                name: 'Daad',
                transliteration: 'Daad',
                sound: 'Daa - Dual',
                forms: {
                    isolated: 'ض',
                    beginning: 'ضـ',
                    middle: 'ـضـ',
                    end: 'ـض'
                },
                examples: [
                    { arabic: 'ضوء', transliteration: 'daw', meaning: 'light' },
                    { arabic: 'أرض', transliteration: 'ard', meaning: 'earth' }
                ],
                learned: false
            },
            {
                id: 16,
                letter: 'ط',
                name: 'Taa',
                transliteration: 'Taa (emphatic)',
                sound: 'Tua - Tuatara',
                forms: {
                    isolated: 'ط',
                    beginning: 'طـ',
                    middle: 'ـطـ',
                    end: 'ـط'
                },
                examples: [
                    { arabic: 'طعام', transliteration: 'taam', meaning: 'food' },
                    { arabic: 'خط', transliteration: 'khat', meaning: 'line' }
                ],
                learned: false
            },
            {
                id: 17,
                letter: 'ظ',
                name: 'Dhaa',
                transliteration: 'Dhaa (emphatic)',
                sound: 'Zua - Zua',
                forms: {
                    isolated: 'ظ',
                    beginning: 'ظـ',
                    middle: 'ـظـ',
                    end: 'ـظ'
                },
                examples: [
                    { arabic: 'ظل', transliteration: 'dhil', meaning: 'shade' },
                    { arabic: 'حفظ', transliteration: 'hifz', meaning: 'memorization' }
                ],
                learned: false
            },
            {
                id: 18,
                letter: 'ع',
                name: 'Ayn',
                transliteration: 'Ayn',
                sound: 'Ain - Ain',
                forms: {
                    isolated: 'ع',
                    beginning: 'عـ',
                    middle: 'ـعـ',
                    end: 'ـع'
                },
                examples: [
                    { arabic: 'علم', transliteration: 'ilm', meaning: 'knowledge' },
                    { arabic: 'سمع', transliteration: 'sama', meaning: 'hearing' }
                ],
                learned: false
            },
            {
                id: 19,
                letter: 'غ',
                name: 'Ghayn',
                transliteration: 'Ghayn',
                sound: 'Ghai - Ghain',
                forms: {
                    isolated: 'غ',
                    beginning: 'غـ',
                    middle: 'ـغـ',
                    end: 'ـغ'
                },
                examples: [
                    { arabic: 'غار', transliteration: 'ghar', meaning: 'cave' },
                    { arabic: 'بلاغ', transliteration: 'balagh', meaning: 'message' }
                ],
                learned: false
            },
            {
                id: 20,
                letter: 'ف',
                name: 'Faa',
                transliteration: 'Faa',
                sound: 'Fa - Fast',
                forms: {
                    isolated: 'ف',
                    beginning: 'فـ',
                    middle: 'ـفـ',
                    end: 'ـف'
                },
                examples: [
                    { arabic: 'فيل', transliteration: 'fil', meaning: 'elephant' },
                    { arabic: 'صف', transliteration: 'saff', meaning: 'row' }
                ],
                learned: false
            },
            {
                id: 21,
                letter: 'ق',
                name: 'Qaaf',
                transliteration: 'Qaaf',
                sound: 'Qa - Qatar',
                forms: {
                    isolated: 'ق',
                    beginning: 'قـ',
                    middle: 'ـقـ',
                    end: 'ـق'
                },
                examples: [
                    { arabic: 'قرآن', transliteration: 'Quran', meaning: 'Quran' },
                    { arabic: 'حق', transliteration: 'haqq', meaning: 'truth' }
                ],
                learned: false
            },
            {
                id: 22,
                letter: 'ك',
                name: 'Kaaf',
                transliteration: 'Kaaf',
                sound: 'Ka - Kayak',
                forms: {
                    isolated: 'ك',
                    beginning: 'كـ',
                    middle: 'ـكـ',
                    end: 'ـك'
                },
                examples: [
                    { arabic: 'كتاب', transliteration: 'kitab', meaning: 'book' },
                    { arabic: 'ملك', transliteration: 'malik', meaning: 'king' }
                ],
                learned: false
            },
            {
                id: 23,
                letter: 'ل',
                name: 'Laam',
                transliteration: 'Laam',
                sound: 'La - Love',
                forms: {
                    isolated: 'ل',
                    beginning: 'لـ',
                    middle: 'ـلـ',
                    end: 'ـل'
                },
                examples: [
                    { arabic: 'لغة', transliteration: 'lugha', meaning: 'language' },
                    { arabic: 'جمل', transliteration: 'jamal', meaning: 'camel' }
                ],
                learned: false
            },
            {
                id: 24,
                letter: 'م',
                name: 'Meem',
                transliteration: 'Meem',
                sound: 'Me - Me',
                forms: {
                    isolated: 'م',
                    beginning: 'مـ',
                    middle: 'ـمـ',
                    end: 'ـم'
                },
                examples: [
                    { arabic: 'ماء', transliteration: 'maa', meaning: 'water' },
                    { arabic: 'اسم', transliteration: 'ism', meaning: 'name' }
                ],
                learned: false
            },
            {
                id: 25,
                letter: 'ن',
                name: 'Noon',
                transliteration: 'Noon',
                sound: 'Nu - Numb',
                forms: {
                    isolated: 'ن',
                    beginning: 'نـ',
                    middle: 'ـنـ',
                    end: 'ـن'
                },
                examples: [
                    { arabic: 'نور', transliteration: 'nur', meaning: 'light' },
                    { arabic: 'لسان', transliteration: 'lisan', meaning: 'tongue' }
                ],
                learned: false
            },
            {
                id: 26,
                letter: 'ه',
                name: 'Haa',
                transliteration: 'Haa',
                sound: 'Ha - Hat',
                forms: {
                    isolated: 'ه',
                    beginning: 'هـ',
                    middle: 'ـهـ',
                    end: 'ـه'
                },
                examples: [
                    { arabic: 'هدى', transliteration: 'huda', meaning: 'guidance' },
                    { arabic: 'الله', transliteration: 'Allah', meaning: 'Allah' }
                ],
                learned: false
            },
            {
                id: 27,
                letter: 'و',
                name: 'Waaw',
                transliteration: 'Waaw',
                sound: 'Wow - Wow',
                forms: {
                    isolated: 'و',
                    beginning: 'و',
                    middle: 'ـو',
                    end: 'ـو'
                },
                examples: [
                    { arabic: 'ولد', transliteration: 'walad', meaning: 'boy' },
                    { arabic: 'دعوة', transliteration: 'dawa', meaning: 'invitation' }
                ],
                learned: false
            },
            {
                id: 28,
                letter: 'ي',
                name: 'Yaa',
                transliteration: 'Yaa',
                sound: 'Ya - Yard',
                forms: {
                    isolated: 'ي',
                    beginning: 'يـ',
                    middle: 'ـيـ',
                    end: 'ـي'
                },
                examples: [
                    { arabic: 'يوم', transliteration: 'yawm', meaning: 'day' },
                    { arabic: 'هدي', transliteration: 'hady', meaning: 'gift' }
                ],
                learned: false
            }
        ];
        
        this.currentLetter = null;
        this.learnedCount = 0;
        
        this.init();
    }

    init() {
        this.loadProgress();
        this.renderAlphabet();
        this.setupEventListeners();
        this.updateProgress();
    }

    loadProgress() {
        const saved = localStorage.getItem('lesson-1-progress');
        if (saved) {
            const progress = JSON.parse(saved);
            progress.forEach(savedLetter => {
                const letter = this.arabicLetters.find(l => l.id === savedLetter.id);
                if (letter) {
                    letter.learned = savedLetter.learned;
                }
            });
        }
        this.learnedCount = this.arabicLetters.filter(l => l.learned).length;
    }

    saveProgress() {
        const progress = this.arabicLetters.map(letter => ({
            id: letter.id,
            learned: letter.learned
        }));
        localStorage.setItem('lesson-1-progress', JSON.stringify(progress));
    }

    renderAlphabet() {
        const grid = document.getElementById('alphabet-grid');
        grid.innerHTML = '';

        this.arabicLetters.forEach(letter => {
            const letterCard = this.createLetterCard(letter);
            grid.appendChild(letterCard);
        });
    }

    createLetterCard(letter) {
        const card = document.createElement('div');
        card.className = `bg-white rounded-lg shadow-md p-4 text-center cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${letter.learned ? 'border-2 border-primary bg-primary bg-opacity-10' : ''}`;
        
        card.innerHTML = `
            <div class="relative">
                <div class="text-4xl font-arabic text-primary mb-2">${letter.letter}</div>
            </div>
            <div class="text-sm font-semibold text-dark">${letter.name}</div>
            <div class="text-xs text-secondary">${letter.transliteration}</div>
            ${letter.learned ? '<div class="text-primary text-xs mt-1">✓ Learned</div>' : ''}
        `;

        card.addEventListener('click', () => {
            this.selectLetter(letter);
        });

        return card;
    }

    selectLetter(letter) {
        this.currentLetter = letter;
        this.showLetterDetails(letter);
        
        // Scroll to details section
        document.getElementById('letter-details').scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
        });
    }

    showLetterDetails(letter) {
        const detailsSection = document.getElementById('letter-details');
        detailsSection.classList.remove('hidden');

        // Update letter display
        document.getElementById('selected-letter').textContent = letter.letter;
        document.getElementById('letter-name').textContent = letter.name;
        document.getElementById('letter-sound').textContent = `Sound: ${letter.sound}`;



        // Update examples
        const examplesContainer = document.getElementById('example-words');
        examplesContainer.innerHTML = '';
        
        letter.examples.forEach(example => {
            const exampleDiv = document.createElement('div');
            exampleDiv.className = 'p-3 bg-light rounded-lg';
            exampleDiv.innerHTML = `
                <span class="text-2xl font-arabic text-primary">${example.arabic}</span>
                <span class="text-sm text-secondary ml-2">(${example.transliteration} - ${example.meaning})</span>
            `;
            examplesContainer.appendChild(exampleDiv);
        });

        // Update play button based on audio availability
        const playBtn = document.getElementById('play-letter-sound');
        if (letter.audioFile) {
            playBtn.textContent = '🔊 Play Sound';
            playBtn.className = 'bg-primary text-dark px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
            playBtn.disabled = false;
        } else {
            playBtn.textContent = '🔊 Audio Coming Soon';
            playBtn.className = 'bg-gray-300 text-gray-600 px-6 py-3 rounded-lg cursor-not-allowed';
            playBtn.disabled = true;
        }

        // Update mark learned button
        const markLearnedBtn = document.getElementById('mark-learned');
        if (letter.learned) {
            markLearnedBtn.textContent = '✓ Already Learned';
            markLearnedBtn.className = 'w-full bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
            markLearnedBtn.disabled = true;
        } else {
            markLearnedBtn.textContent = '✓ Mark as Learned';
            markLearnedBtn.className = 'w-full bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
            markLearnedBtn.disabled = false;
        }

        // Update next letter button
        this.updateNextLetterButton();
    }

    updateNextLetterButton() {
        const nextLetterBtn = document.getElementById('next-letter');
        nextLetterBtn.textContent = 'NEXT';
    }

    markLetterAsLearned() {
        if (this.currentLetter && !this.currentLetter.learned) {
            this.currentLetter.learned = true;
            this.learnedCount++;
            
            this.saveProgress();
            this.renderAlphabet();
            this.updateProgress();
            this.showLetterDetails(this.currentLetter);
            
            // Show success message
            this.showNotification(`Great! You've learned ${this.currentLetter.name} (${this.currentLetter.letter})`, 'success');
        }
    }

    updateProgress() {
        const totalLetters = this.arabicLetters.length;
        const percentage = Math.round((this.learnedCount / totalLetters) * 100);
        
        // Update lesson progress bar
        document.getElementById('lesson-progress').textContent = `${this.learnedCount}/${totalLetters}`;
        document.getElementById('lesson-progress-fill').style.width = `${percentage}%`;
        
        // Update summary
        document.getElementById('letters-learned').textContent = this.learnedCount;
        document.getElementById('completion-percentage').textContent = `${percentage}%`;
        
        // Enable complete lesson button if all letters learned
        const completeBtn = document.getElementById('complete-lesson');
        if (this.learnedCount === totalLetters) {
            completeBtn.textContent = 'Complete Lesson ✓';
            completeBtn.className = 'flex-1 bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
        }
    }

    setupEventListeners() {
        // Play sound button
        document.getElementById('play-letter-sound').addEventListener('click', () => {
            if (this.currentLetter) {
                this.playLetterSound(this.currentLetter);
            }
        });

        // Mark as learned button
        document.getElementById('mark-learned').addEventListener('click', () => {
            this.markLetterAsLearned();
        });

        // Complete lesson button
        document.getElementById('complete-lesson').addEventListener('click', () => {
            this.completeLesson();
        });

        // Next letter button
        document.getElementById('next-letter').addEventListener('click', () => {
            this.goToNextLetter();
        });

        // Previous letter button
        document.getElementById('previous-letter').addEventListener('click', () => {
            this.goToPreviousLetter();
        });
    }

    playLetterSound(letter) {
        if (letter.audioFile) {
            // Play actual audio file
            const audio = new Audio(letter.audioFile);

            // Show loading notification
            this.showNotification(`Playing ${letter.name} (${letter.letter})`, 'info');

            // Play the audio
            audio.play().then(() => {
                console.log(`Playing audio for letter: ${letter.letter}`);
            }).catch((error) => {
                console.error('Error playing audio:', error);
                this.showNotification(`Could not play audio for ${letter.name}`, 'warning');
            });

            // Optional: Show completion notification when audio ends
            audio.addEventListener('ended', () => {
                this.showNotification(`Finished playing ${letter.name}`, 'success');
            });
        } else {
            // Fallback for letters without audio files
            this.showNotification(`Audio not available for ${letter.name} yet`, 'warning');
            console.log(`No audio file for letter: ${letter.letter}`);
        }
    }

    completeLesson() {
        if (this.learnedCount === this.arabicLetters.length) {
            // Mark lesson as completed in main app
            const mainAppProgress = JSON.parse(localStorage.getItem('qaidah-progress') || '[]');
            const lesson1 = mainAppProgress.find(l => l.id === 1);
            if (lesson1) {
                lesson1.completed = true;
                localStorage.setItem('qaidah-progress', JSON.stringify(mainAppProgress));
            }
            
            this.showNotification('Congratulations! You have completed Lesson 1!', 'success');
            
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
        } else {
            this.showNotification('Please learn all letters before completing the lesson.', 'warning');
        }
    }

    goToNextLetter() {
        if (!this.currentLetter) {
            // If no letter is selected, start with the first letter (Alif)
            this.selectLetter(this.arabicLetters[0]);
            return;
        }

        // Find the current letter index
        const currentIndex = this.arabicLetters.findIndex(l => l.id === this.currentLetter.id);

        // Go to next letter, or wrap to first if at the end
        const nextIndex = (currentIndex + 1) % this.arabicLetters.length;
        const nextLetter = this.arabicLetters[nextIndex];

        this.selectLetter(nextLetter);
    }

    goToPreviousLetter() {
        if (!this.currentLetter) {
            // If no letter is selected, start with the last letter (Yaa)
            this.selectLetter(this.arabicLetters[this.arabicLetters.length - 1]);
            return;
        }

        // Find the current letter index
        const currentIndex = this.arabicLetters.findIndex(l => l.id === this.currentLetter.id);

        // Go to previous letter, or wrap to last if at the beginning
        const previousIndex = currentIndex === 0 ? this.arabicLetters.length - 1 : currentIndex - 1;
        const previousLetter = this.arabicLetters[previousIndex];

        this.selectLetter(previousLetter);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${colors[type]}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the lesson when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ArabicAlphabetLesson();
});
