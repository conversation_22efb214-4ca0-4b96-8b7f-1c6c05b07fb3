// Main application logic
class QaidahApp {
    constructor() {
        this.currentLesson = null;
        this.lessons = [
            {
                id: 1,
                title: "Arabic Alphabet - الحروف الهجائية",
                description: "Learn the 28 letters of the Arabic alphabet",
                difficulty: "Beginner",
                duration: "15 min",
                completed: false
            },
            {
                id: 2,
                title: "Jumbled Alphabet - قم",
                description: "Practice recognizing Arabic letters when they're jumbled up",
                difficulty: "Beginner",
                duration: "15 min",
                completed: false
            },
            {
                id: 3,
                title: "Letter Shapes - أشكال الحروف",
                description: "Understanding how letters change shape in different positions",
                difficulty: "Beginner",
                duration: "25 min",
                completed: false
            },
            {
                id: 4,
                title: "Short Vowels - الحركات القصيرة",
                description: "Fatha, Kasra, and Damma",
                difficulty: "Beginner",
                duration: "25 min",
                completed: false
            },
            {
                id: 5,
                title: "Sukoon and Shadda - السكون والشدة",
                description: "Learn about silence and emphasis marks",
                difficulty: "Intermediate",
                duration: "20 min",
                completed: false
            },
            {
                id: 6,
                title: "Long Vowels - الحروف المدية",
                description: "<PERSON><PERSON>, Waw, and Ya as long vowels",
                difficulty: "Intermediate",
                duration: "30 min",
                completed: false
            }
        ];
        
        this.init();
    }

    init() {
        this.loadProgress();
        this.renderLessons();
        this.setupEventListeners();
        this.updateProgressDisplay();
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Start learning button
        const startBtn = document.getElementById('start-learning-btn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.scrollToSection('lessons');
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this.scrollToSection(targetId);
            });
        });

        // Reset progress button
        const resetBtn = document.getElementById('reset-progress-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.showResetConfirmation();
            });
        }
    }

    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }
    }

    renderLessons() {
        const lessonsGrid = document.getElementById('lessons-grid');
        if (!lessonsGrid) return;

        lessonsGrid.innerHTML = '';

        this.lessons.forEach(lesson => {
            const lessonCard = this.createLessonCard(lesson);
            lessonsGrid.appendChild(lessonCard);
        });
    }

    createLessonCard(lesson) {
        const card = document.createElement('div');
        card.className = `bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border ${lesson.completed ? 'border-primary border-opacity-50' : 'border-gray-200'}`;

        card.innerHTML = `
            <div class="p-6">
                <!-- Header with lesson number and title -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                            <span class="text-dark font-bold text-lg">${lesson.id}</span>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-dark leading-tight">${lesson.title}</h4>
                            <p class="text-sm text-secondary">Lesson ${lesson.id}</p>
                        </div>
                    </div>
                    ${lesson.completed ? '<div class="text-primary"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></div>' : ''}
                </div>

                <!-- Description -->
                <p class="text-gray-600 mb-4 text-sm leading-relaxed">${lesson.description}</p>

                <!-- Lesson details -->
                <div class="flex items-center justify-between mb-6 text-sm">
                    <span class="text-secondary">${lesson.difficulty}</span>
                </div>

                <!-- Action button -->
                <button class="w-full bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors lesson-btn font-semibold" data-lesson-id="${lesson.id}">
                    ${lesson.completed ? 'Review Lesson' : 'Start Lesson'}
                </button>
            </div>
        `;

        // Add click event to lesson button
        const lessonBtn = card.querySelector('.lesson-btn');
        lessonBtn.addEventListener('click', () => {
            this.startLesson(lesson.id);
        });

        return card;
    }

    startLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        // Navigate to dedicated lesson pages
        if (lessonId === 1) {
            window.location.href = './lessons/lesson-1.html';
            return;
        }

        if (lessonId === 2) {
            window.location.href = './lessons/lesson-2.html';
            return;
        }

        if (lessonId === 3) {
            window.location.href = './lessons/lesson-3.html';
            return;
        }

        // For other lessons, show modal (placeholder)
        this.showLessonModal(lesson);
    }

    showLessonModal(lesson) {
        // Create a modal for the lesson
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-2xl font-bold text-dark">${lesson.title}</h3>
                        <button class="text-gray-500 hover:text-gray-700 close-modal">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <p class="text-gray-600 mb-6">${lesson.description}</p>
                    
                    <div class="lesson-content">
                        <p class="mb-4">This is a placeholder for lesson content. In a complete implementation, this would include:</p>
                        <ul class="list-disc list-inside mb-6 space-y-2">
                            <li>Interactive Arabic text with audio</li>
                            <li>Step-by-step pronunciation guide</li>
                            <li>Practice exercises</li>
                            <li>Progress tracking</li>
                        </ul>
                        
                        <div class="bg-light p-4 rounded-lg mb-6">
                            <p class="arabic-text text-center mb-4">بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ</p>
                            <p class="text-center text-sm text-gray-600">Click on the Arabic text to hear pronunciation</p>
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button class="flex-1 bg-primary text-dark py-2 rounded-lg hover:bg-opacity-90 transition-colors complete-lesson font-semibold" data-lesson-id="${lesson.id}">
                            Complete Lesson
                        </button>
                        <button class="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors close-modal">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        modal.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });

        const completeBtn = modal.querySelector('.complete-lesson');
        completeBtn.addEventListener('click', () => {
            this.completeLesson(lesson.id);
            document.body.removeChild(modal);
        });

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    completeLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (lesson) {
            lesson.completed = true;
            this.saveProgress();
            this.renderLessons();
            this.updateProgressDisplay();
            
            // Show completion message
            this.showNotification('Lesson completed! Well done!', 'success');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }

    updateProgressDisplay() {
        const completedLessons = this.lessons.filter(l => l.completed).length;
        const totalLessons = this.lessons.length;
        const percentage = Math.round((completedLessons / totalLessons) * 100);
        
        const progressPercentage = document.getElementById('progress-percentage');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        if (progressPercentage) progressPercentage.textContent = `${percentage}%`;
        if (progressFill) progressFill.style.width = `${percentage}%`;
        if (progressText) {
            progressText.textContent = `${completedLessons} of ${totalLessons} lessons completed`;
        }
    }

    saveProgress() {
        localStorage.setItem('qaidah-progress', JSON.stringify(this.lessons));
    }

    loadProgress() {
        const saved = localStorage.getItem('qaidah-progress');
        if (saved) {
            const savedLessons = JSON.parse(saved);
            // Merge saved progress with current lessons
            savedLessons.forEach(savedLesson => {
                const lesson = this.lessons.find(l => l.id === savedLesson.id);
                if (lesson) {
                    lesson.completed = savedLesson.completed;
                }
            });
        }
    }

    showResetConfirmation() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-dark mb-2">Reset All Progress?</h3>
                        <p class="text-secondary">This will permanently delete all your lesson progress and cannot be undone. Are you sure you want to continue?</p>
                    </div>

                    <div class="flex space-x-3">
                        <button class="flex-1 bg-gray-200 text-dark py-3 rounded-lg hover:bg-gray-300 transition-colors font-semibold cancel-reset">
                            Cancel
                        </button>
                        <button class="flex-1 bg-red-500 text-white py-3 rounded-lg hover:bg-red-600 transition-colors font-semibold confirm-reset">
                            Reset Progress
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        const cancelBtn = modal.querySelector('.cancel-reset');
        const confirmBtn = modal.querySelector('.confirm-reset');

        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        confirmBtn.addEventListener('click', () => {
            this.resetAllProgress();
            document.body.removeChild(modal);
        });

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    resetAllProgress() {
        // Reset all lessons to incomplete
        this.lessons.forEach(lesson => {
            lesson.completed = false;
        });

        // Clear all localStorage data
        localStorage.removeItem('qaidah-progress');
        localStorage.removeItem('lesson-1-progress');
        localStorage.removeItem('lesson-2-progress');
        localStorage.removeItem('lesson-3-progress');

        // Re-render the UI
        this.renderLessons();
        this.updateProgressDisplay();

        // Show success message
        this.showNotification('All progress has been reset successfully!', 'success');
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new QaidahApp();
});
