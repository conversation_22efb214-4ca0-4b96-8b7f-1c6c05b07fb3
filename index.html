<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Read Qaidah - Your Essential Pathway to Reciting the Quran</title>
    <meta name="description" content="Learn to recite the Quran with our interactive Qaidah lessons. Step-by-step guidance for beginners.">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link href="./css/styles.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="The Read Qaidah">
    <meta property="og:description" content="Your essential pathway to reciting the Quran">
    <meta property="og:type" content="website">
</head>
<body class="bg-light font-sans">
    <!-- Header -->
    <header class="bg-dark text-white shadow-lg">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <span class="text-dark font-bold text-xl">ق</span>
                    </div>
                    <h1 class="text-xl md:text-2xl font-bold">The Read Qaidah</h1>
                </div>

                <div class="hidden md:flex space-x-6">
                    <a href="#home" class="hover:text-primary transition-colors">Home</a>
                    <a href="#lessons" class="hover:text-primary transition-colors">Lessons</a>
                    <a href="#progress" class="hover:text-primary transition-colors">Progress</a>
                    <a href="#about" class="hover:text-primary transition-colors">About</a>
                </div>
                
                <!-- Mobile menu button -->
                <button id="mobile-menu-btn" class="md:hidden">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 space-y-2">
                <a href="#home" class="block py-2 hover:text-primary transition-colors">Home</a>
                <a href="#lessons" class="block py-2 hover:text-primary transition-colors">Lessons</a>
                <a href="#progress" class="block py-2 hover:text-primary transition-colors">Progress</a>
                <a href="#about" class="block py-2 hover:text-primary transition-colors">About</a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="bg-gradient-to-br from-secondary to-dark text-white py-16">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">
                    بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
                </h2>
                <p class="text-xl md:text-2xl mb-8 font-arabic">
                    Your Essential Pathway to Reciting the Quran
                </p>
                <p class="text-lg mb-8 max-w-2xl mx-auto">
                    Learn to read the Quran with proper pronunciation through our interactive Qaidah lessons. 
                    Perfect for beginners and those looking to improve their recitation.
                </p>
                <button id="start-learning-btn" class="bg-primary text-dark px-8 py-4 rounded-lg text-lg font-semibold hover:bg-opacity-90 transition-all duration-200 transform hover:scale-105">
                    Start Learning
                </button>
            </div>
        </section>

        <!-- Lessons Section -->
        <section id="lessons" class="py-16">
            <div class="container mx-auto px-4">
                <h3 class="text-3xl font-bold text-center mb-12 text-dark">Qaidah Lessons</h3>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6" id="lessons-grid">
                    <!-- Lessons will be dynamically loaded here -->
                </div>
            </div>
        </section>

        <!-- Progress Section -->
        <section id="progress" class="bg-white py-16">
            <div class="container mx-auto px-4">
                <h3 class="text-3xl font-bold text-center mb-12 text-dark">Your Progress</h3>

                <div class="max-w-2xl mx-auto">
                    <div class="bg-white rounded-lg p-6 shadow-md">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-semibold">Overall Progress</span>
                            <span id="progress-percentage" class="text-primary font-bold">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
                        </div>
                        <p class="text-sm text-gray-600 mt-2" id="progress-text">Start your first lesson to begin tracking progress</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reset Progress Section -->
        <section class="py-16 bg-light">
            <div class="container mx-auto px-4">
                <div class="max-w-md mx-auto text-center">
                    <h3 class="text-2xl font-bold text-dark mb-4">Reset Progress</h3>
                    <p class="text-secondary mb-6">Clear all lesson progress and start fresh. This action cannot be undone.</p>

                    <button id="reset-progress-btn" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors font-semibold">
                        Reset All Progress
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-4">&copy; 2024 The Read Qaidah. Built with love for the Ummah.</p>
            <p class="text-sm text-gray-400">May Allah accept our efforts in learning His Book.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="./js/app.js"></script>
    <script src="./js/audio-player.js"></script>
    <script src="./js/progress-tracker.js"></script>
</body>
</html>
