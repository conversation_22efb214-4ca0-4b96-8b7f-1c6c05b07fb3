// Progress tracking functionality
class ProgressTracker {
    constructor() {
        this.userProgress = {
            lessonsCompleted: [],
            totalTimeSpent: 0,
            currentStreak: 0,
            longestStreak: 0,
            lastStudyDate: null,
            achievements: [],
            skillLevels: {
                alphabet: 0,
                pronunciation: 0,
                fluency: 0,
                memorization: 0
            }
        };
        
        this.achievements = [
            {
                id: 'first_lesson',
                title: 'First Steps',
                description: 'Complete your first lesson',
                icon: '🌟',
                unlocked: false
            },
            {
                id: 'week_streak',
                title: 'Dedicated Learner',
                description: 'Study for 7 days in a row',
                icon: '🔥',
                unlocked: false
            },
            {
                id: 'alphabet_master',
                title: 'Alphabet Master',
                description: 'Complete all alphabet lessons',
                icon: '📚',
                unlocked: false
            },
            {
                id: 'pronunciation_pro',
                title: 'Pronunciation Pro',
                description: 'Perfect pronunciation in 10 lessons',
                icon: '🎯',
                unlocked: false
            },
            {
                id: 'consistent_learner',
                title: 'Consistent Learner',
                description: 'Study for 30 days',
                icon: '💎',
                unlocked: false
            }
        ];
        
        this.init();
    }

    init() {
        this.loadProgress();
        this.updateStreak();
    }

    loadProgress() {
        const saved = localStorage.getItem('qaidah-user-progress');
        if (saved) {
            this.userProgress = { ...this.userProgress, ...JSON.parse(saved) };
        }
        
        const savedAchievements = localStorage.getItem('qaidah-achievements');
        if (savedAchievements) {
            const saved = JSON.parse(savedAchievements);
            this.achievements.forEach(achievement => {
                const savedAchievement = saved.find(a => a.id === achievement.id);
                if (savedAchievement) {
                    achievement.unlocked = savedAchievement.unlocked;
                }
            });
        }
    }

    saveProgress() {
        localStorage.setItem('qaidah-user-progress', JSON.stringify(this.userProgress));
        localStorage.setItem('qaidah-achievements', JSON.stringify(this.achievements));
    }

    completeLesson(lessonId, timeSpent = 0, skillsImproved = []) {
        // Add lesson to completed list if not already there
        if (!this.userProgress.lessonsCompleted.includes(lessonId)) {
            this.userProgress.lessonsCompleted.push(lessonId);
        }
        
        // Update time spent
        this.userProgress.totalTimeSpent += timeSpent;
        
        // Update skill levels
        skillsImproved.forEach(skill => {
            if (this.userProgress.skillLevels[skill] !== undefined) {
                this.userProgress.skillLevels[skill] = Math.min(100, this.userProgress.skillLevels[skill] + 10);
            }
        });
        
        // Update study date and streak
        this.updateStudyDate();
        
        // Check for achievements
        this.checkAchievements();
        
        // Save progress
        this.saveProgress();
        
        return this.getProgressSummary();
    }

    updateStudyDate() {
        const today = new Date().toDateString();
        const lastStudy = this.userProgress.lastStudyDate;
        
        if (lastStudy !== today) {
            if (lastStudy) {
                const lastDate = new Date(lastStudy);
                const todayDate = new Date(today);
                const diffTime = todayDate - lastDate;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays === 1) {
                    // Consecutive day
                    this.userProgress.currentStreak++;
                } else {
                    // Streak broken
                    this.userProgress.currentStreak = 1;
                }
            } else {
                // First study session
                this.userProgress.currentStreak = 1;
            }
            
            this.userProgress.lastStudyDate = today;
            
            // Update longest streak
            if (this.userProgress.currentStreak > this.userProgress.longestStreak) {
                this.userProgress.longestStreak = this.userProgress.currentStreak;
            }
        }
    }

    updateStreak() {
        const today = new Date().toDateString();
        const lastStudy = this.userProgress.lastStudyDate;
        
        if (lastStudy && lastStudy !== today) {
            const lastDate = new Date(lastStudy);
            const todayDate = new Date(today);
            const diffTime = todayDate - lastDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays > 1) {
                // Streak broken
                this.userProgress.currentStreak = 0;
                this.saveProgress();
            }
        }
    }

    checkAchievements() {
        const newAchievements = [];
        
        // First lesson achievement
        if (!this.achievements.find(a => a.id === 'first_lesson').unlocked && 
            this.userProgress.lessonsCompleted.length >= 1) {
            this.unlockAchievement('first_lesson');
            newAchievements.push('first_lesson');
        }
        
        // Week streak achievement
        if (!this.achievements.find(a => a.id === 'week_streak').unlocked && 
            this.userProgress.currentStreak >= 7) {
            this.unlockAchievement('week_streak');
            newAchievements.push('week_streak');
        }
        
        // Alphabet master achievement (assuming lessons 1-3 are alphabet related)
        if (!this.achievements.find(a => a.id === 'alphabet_master').unlocked && 
            [1, 2, 3].every(id => this.userProgress.lessonsCompleted.includes(id))) {
            this.unlockAchievement('alphabet_master');
            newAchievements.push('alphabet_master');
        }
        
        // Consistent learner achievement
        if (!this.achievements.find(a => a.id === 'consistent_learner').unlocked && 
            this.userProgress.longestStreak >= 30) {
            this.unlockAchievement('consistent_learner');
            newAchievements.push('consistent_learner');
        }
        
        // Show achievement notifications
        newAchievements.forEach(achievementId => {
            this.showAchievementNotification(achievementId);
        });
        
        return newAchievements;
    }

    unlockAchievement(achievementId) {
        const achievement = this.achievements.find(a => a.id === achievementId);
        if (achievement) {
            achievement.unlocked = true;
            this.userProgress.achievements.push({
                id: achievementId,
                unlockedAt: new Date().toISOString()
            });
        }
    }

    showAchievementNotification(achievementId) {
        const achievement = this.achievements.find(a => a.id === achievementId);
        if (!achievement) return;
        
        const notification = document.createElement('div');
        notification.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-2xl p-6 z-50 max-w-sm w-full mx-4';
        notification.innerHTML = `
            <div class="text-center">
                <div class="text-4xl mb-4">${achievement.icon}</div>
                <h3 class="text-xl font-bold text-dark mb-2">Achievement Unlocked!</h3>
                <h4 class="text-lg font-semibold text-primary mb-2">${achievement.title}</h4>
                <p class="text-gray-600 mb-4">${achievement.description}</p>
                <button class="bg-primary text-dark px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors close-achievement font-semibold">
                    Awesome!
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Add close functionality
        notification.querySelector('.close-achievement').addEventListener('click', () => {
            document.body.removeChild(notification);
        });
        
        // Auto-close after 5 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 5000);
    }

    getProgressSummary() {
        return {
            lessonsCompleted: this.userProgress.lessonsCompleted.length,
            totalTimeSpent: this.userProgress.totalTimeSpent,
            currentStreak: this.userProgress.currentStreak,
            longestStreak: this.userProgress.longestStreak,
            skillLevels: this.userProgress.skillLevels,
            achievements: this.achievements.filter(a => a.unlocked)
        };
    }

    getDetailedProgress() {
        const summary = this.getProgressSummary();
        
        return {
            ...summary,
            percentageComplete: this.calculateOverallProgress(),
            nextMilestone: this.getNextMilestone(),
            studyStreak: this.getStreakInfo(),
            recentActivity: this.getRecentActivity()
        };
    }

    calculateOverallProgress() {
        // This would be based on total lessons available
        const totalLessons = 6; // Update this based on actual lesson count
        return Math.round((this.userProgress.lessonsCompleted.length / totalLessons) * 100);
    }

    getNextMilestone() {
        const unlockedAchievements = this.achievements.filter(a => !a.unlocked);
        if (unlockedAchievements.length > 0) {
            return unlockedAchievements[0];
        }
        return null;
    }

    getStreakInfo() {
        return {
            current: this.userProgress.currentStreak,
            longest: this.userProgress.longestStreak,
            lastStudy: this.userProgress.lastStudyDate
        };
    }

    getRecentActivity() {
        // This would return recent lesson completions, achievements, etc.
        return {
            recentLessons: this.userProgress.lessonsCompleted.slice(-5),
            recentAchievements: this.userProgress.achievements.slice(-3)
        };
    }

    exportProgress() {
        return {
            userProgress: this.userProgress,
            achievements: this.achievements,
            exportDate: new Date().toISOString()
        };
    }

    importProgress(progressData) {
        if (progressData.userProgress) {
            this.userProgress = { ...this.userProgress, ...progressData.userProgress };
        }
        
        if (progressData.achievements) {
            progressData.achievements.forEach(importedAchievement => {
                const achievement = this.achievements.find(a => a.id === importedAchievement.id);
                if (achievement) {
                    achievement.unlocked = importedAchievement.unlocked;
                }
            });
        }
        
        this.saveProgress();
    }

    resetProgress() {
        if (confirm('Are you sure you want to reset all progress? This cannot be undone.')) {
            localStorage.removeItem('qaidah-user-progress');
            localStorage.removeItem('qaidah-achievements');
            localStorage.removeItem('qaidah-progress');
            
            // Reset in-memory data
            this.userProgress = {
                lessonsCompleted: [],
                totalTimeSpent: 0,
                currentStreak: 0,
                longestStreak: 0,
                lastStudyDate: null,
                achievements: [],
                skillLevels: {
                    alphabet: 0,
                    pronunciation: 0,
                    fluency: 0,
                    memorization: 0
                }
            };
            
            this.achievements.forEach(achievement => {
                achievement.unlocked = false;
            });
            
            // Refresh the page to reflect changes
            window.location.reload();
        }
    }
}

// Initialize progress tracker
const progressTracker = new ProgressTracker();
