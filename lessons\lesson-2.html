<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lesson 2: Jumbled Alphabet - The Read Qaidah</title>
    <meta name="description" content="Practice recognizing Arabic letters when they're jumbled up">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body class="bg-light font-sans">
    <!-- Header -->
    <header class="bg-dark text-white shadow-lg">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="../index.html" class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <span class="text-dark font-bold text-xl">ق</span>
                    </a>
                    <div>
                        <h1 class="text-lg font-bold">The Read Qaidah</h1>
                        <p class="text-sm text-gray-300">Lesson 2: Jumbled Alphabet</p>
                    </div>
                </div>
                
                <a href="../index.html" class="bg-primary text-dark px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    ← Home
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Lesson Header -->
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-6">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                    <span class="text-dark font-bold text-2xl font-arabic">قم</span>
                </div>
                <div>
                    <h2 class="text-4xl font-bold text-dark mb-2">Jumbled Alphabet</h2>
                    <p class="text-lg text-secondary">Lesson 2</p>
                </div>
            </div>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto mb-4">
                Let's read the letters of the alphabet when the order is jumbled up.
                Click on each letter to identify it and hear its pronunciation.
            </p>
            <div class="bg-white rounded-lg p-4 max-w-md mx-auto shadow-md">
                <p class="text-sm text-secondary mb-2">Practice Goal:</p>
                <p class="font-semibold text-dark">Recognize all 28 letters in random order</p>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-semibold text-secondary">Recognition Progress</span>
                <span id="lesson-progress" class="text-sm font-bold text-primary">0/28</span>
            </div>
            <div class="progress-bar">
                <div id="lesson-progress-fill" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <!-- Jumbled Alphabet Grid -->
        <div class="max-w-4xl mx-auto mb-12">
            <div class="grid grid-cols-6 gap-3 md:gap-4" id="jumbled-grid">
                <!-- Letters will be dynamically loaded here -->
            </div>
        </div>

        <!-- Letter Identification Panel -->
        <div id="identification-panel" class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8 hidden">
            <div class="text-center mb-6">
                <div id="selected-letter" class="text-8xl font-arabic text-primary mb-4">ا</div>
                <div class="space-y-2">
                    <h3 id="letter-name" class="text-2xl font-bold text-dark">?</h3>
                    <p id="letter-sound" class="text-lg text-secondary">Click "Reveal" to see the answer</p>
                </div>
            </div>
            
            <div class="flex space-x-4 mb-6">
                <button id="reveal-answer" class="flex-1 bg-secondary text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    Reveal Answer
                </button>
                <button id="play-letter-sound" class="flex-1 bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold" disabled>
                    🔊 Play Sound
                </button>
            </div>
            
            <div class="flex space-x-3">
                <button id="mark-correct" class="flex-1 bg-green-500 text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold" disabled>
                    ✓ I Got It Right
                </button>
                <button id="mark-incorrect" class="flex-1 bg-red-500 text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold" disabled>
                    ✗ I Need Practice
                </button>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex flex-wrap gap-4 justify-center">
                <button id="shuffle-letters" class="bg-primary text-dark px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    🔀 Shuffle Again
                </button>
                <button id="show-all-answers" class="bg-secondary text-white px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    👁️ Show All Answers
                </button>
                <button id="reset-progress" class="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    🔄 Reset Progress
                </button>
            </div>
        </div>

        <!-- Lesson Summary -->
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-dark mb-4">Lesson Summary</h3>
            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-light rounded-lg">
                    <div class="text-2xl font-bold text-green-500" id="correct-count">0</div>
                    <div class="text-sm text-secondary">Correct</div>
                </div>
                <div class="text-center p-4 bg-light rounded-lg">
                    <div class="text-2xl font-bold text-red-500" id="incorrect-count">0</div>
                    <div class="text-sm text-secondary">Need Practice</div>
                </div>
                <div class="text-center p-4 bg-light rounded-lg">
                    <div class="text-2xl font-bold text-primary" id="completion-percentage">0%</div>
                    <div class="text-sm text-secondary">Completion</div>
                </div>
            </div>
            
            <div class="text-center">
                <button id="complete-lesson" class="bg-primary text-dark px-8 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    Complete Lesson
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-2">Practice makes perfect in learning Arabic</p>
            <p class="text-sm text-gray-400">The Read Qaidah - Lesson 2</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../js/lesson-2.js"></script>
    <script src="../js/audio-player.js"></script>
</body>
</html>
