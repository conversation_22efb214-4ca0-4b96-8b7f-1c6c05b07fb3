// Lesson 2: Jumbled Alphabet
class JumbledAlphabetLesson {
    constructor() {
        this.arabicLetters = [
            { id: 1, letter: 'ا', name: '<PERSON><PERSON>', sound: 'A - Arm', audioFile: '../assets/audio/alif.m4a' },
            { id: 2, letter: 'ب', name: '<PERSON>a', sound: 'Ba - <PERSON>' },
            { id: 3, letter: 'ت', name: 'Taa', sound: 'Ta - Task' },
            { id: 4, letter: 'ث', name: 'Thaa', sound: 'Tha - That' },
            { id: 5, letter: 'ج', name: '<PERSON><PERSON>', sound: '<PERSON> - <PERSON>' },
            { id: 6, letter: 'ح', name: '<PERSON><PERSON>', sound: 'Ha - Hand, Hat' },
            { id: 7, letter: 'خ', name: 'Kha<PERSON>', sound: 'Kha - Khaki' },
            { id: 8, letter: 'د', name: '<PERSON>', sound: 'Da - Da<PERSON>, <PERSON>' },
            { id: 9, letter: 'ذ', name: '<PERSON><PERSON>', sound: '<PERSON><PERSON> - <PERSON><PERSON>' },
            { id: 10, letter: 'ر', name: '<PERSON><PERSON>', sound: 'Ra - <PERSON>' },
            { id: 11, letter: 'ز', name: '<PERSON>aa<PERSON>', sound: 'Za - Z<PERSON>' },
            { id: 12, letter: 'س', name: 'Seen', sound: 'Se - Sell' },
            { id: 13, letter: 'ش', name: 'Sheen', sound: 'She - Shell' },
            { id: 14, letter: 'ص', name: 'Saad', sound: 'Saa - Swab' },
            { id: 15, letter: 'ض', name: 'Daad', sound: 'Daa - Dual' },
            { id: 16, letter: 'ط', name: 'Taa', sound: 'Tua - Tuatara' },
            { id: 17, letter: 'ظ', name: 'Dhaa', sound: 'Zua - Zua' },
            { id: 18, letter: 'ع', name: 'Ayn', sound: 'Ain - Ain' },
            { id: 19, letter: 'غ', name: 'Ghayn', sound: 'Ghai - Ghain' },
            { id: 20, letter: 'ف', name: 'Faa', sound: 'Fa - Fast' },
            { id: 21, letter: 'ق', name: 'Qaaf', sound: 'Qa - Qatar' },
            { id: 22, letter: 'ك', name: 'Kaaf', sound: 'Ka - Kayak' },
            { id: 23, letter: 'ل', name: 'Laam', sound: 'La - Love' },
            { id: 24, letter: 'م', name: 'Meem', sound: 'Me - Me' },
            { id: 25, letter: 'ن', name: 'Noon', sound: 'Nu - Numb' },
            { id: 26, letter: 'ه', name: 'Haa', sound: 'Ha - Hat' },
            { id: 27, letter: 'و', name: 'Waaw', sound: 'Wow - Wow' },
            { id: 28, letter: 'ي', name: 'Yaa', sound: 'Ya - Yard' }
        ];
        
        this.jumbledLetters = [];
        this.currentLetter = null;
        this.correctCount = 0;
        this.incorrectCount = 0;
        this.answersRevealed = false;
        this.letterStates = {}; // Track which letters have been attempted
        
        this.init();
    }

    init() {
        this.loadProgress();
        this.shuffleLetters();
        this.renderJumbledGrid();
        this.setupEventListeners();
        this.updateProgress();
    }

    loadProgress() {
        const saved = localStorage.getItem('lesson-2-progress');
        if (saved) {
            const progress = JSON.parse(saved);
            this.correctCount = progress.correctCount || 0;
            this.incorrectCount = progress.incorrectCount || 0;
            this.letterStates = progress.letterStates || {};
        }
    }

    saveProgress() {
        const progress = {
            correctCount: this.correctCount,
            incorrectCount: this.incorrectCount,
            letterStates: this.letterStates
        };
        localStorage.setItem('lesson-2-progress', JSON.stringify(progress));
    }

    shuffleLetters() {
        // Create a copy of the letters array and shuffle it
        this.jumbledLetters = [...this.arabicLetters];
        for (let i = this.jumbledLetters.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.jumbledLetters[i], this.jumbledLetters[j]] = [this.jumbledLetters[j], this.jumbledLetters[i]];
        }
    }

    renderJumbledGrid() {
        const grid = document.getElementById('jumbled-grid');
        grid.innerHTML = '';

        this.jumbledLetters.forEach((letter, index) => {
            const letterCard = this.createLetterCard(letter, index);
            grid.appendChild(letterCard);
        });
    }

    createLetterCard(letter, index) {
        const card = document.createElement('div');
        const state = this.letterStates[letter.id];
        
        let cardClass = 'bg-white rounded-lg shadow-md p-4 text-center cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105';
        
        if (state === 'correct') {
            cardClass += ' border-2 border-green-500 bg-green-50';
        } else if (state === 'incorrect') {
            cardClass += ' border-2 border-red-500 bg-red-50';
        }
        
        card.className = cardClass;
        
        card.innerHTML = `
            <div class="relative">
                <div class="text-4xl font-arabic text-primary mb-2">${letter.letter}</div>
                ${letter.audioFile ? '<div class="absolute -top-1 -right-1 text-xs">🔊</div>' : ''}
                ${state === 'correct' ? '<div class="text-green-500 text-xs mt-1">✓</div>' : ''}
                ${state === 'incorrect' ? '<div class="text-red-500 text-xs mt-1">✗</div>' : ''}
            </div>
        `;

        card.addEventListener('click', () => {
            this.selectLetter(letter);
        });

        return card;
    }

    selectLetter(letter) {
        this.currentLetter = letter;
        this.showIdentificationPanel(letter);
        
        // Scroll to identification panel
        document.getElementById('identification-panel').scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
        });
    }

    showIdentificationPanel(letter) {
        const panel = document.getElementById('identification-panel');
        panel.classList.remove('hidden');

        // Reset panel state
        this.answersRevealed = false;
        
        // Update letter display
        document.getElementById('selected-letter').textContent = letter.letter;
        document.getElementById('letter-name').textContent = '?';
        document.getElementById('letter-sound').textContent = 'Click "Reveal" to see the answer';

        // Reset buttons
        this.updatePanelButtons();
    }

    updatePanelButtons() {
        const revealBtn = document.getElementById('reveal-answer');
        const playBtn = document.getElementById('play-letter-sound');
        const correctBtn = document.getElementById('mark-correct');
        const incorrectBtn = document.getElementById('mark-incorrect');

        if (this.answersRevealed) {
            revealBtn.textContent = 'Answer Revealed';
            revealBtn.disabled = true;
            revealBtn.className = 'flex-1 bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
            
            playBtn.disabled = !this.currentLetter.audioFile;
            playBtn.className = this.currentLetter.audioFile ? 
                'flex-1 bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold' :
                'flex-1 bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
            
            correctBtn.disabled = false;
            incorrectBtn.disabled = false;
            correctBtn.className = 'flex-1 bg-green-500 text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
            incorrectBtn.className = 'flex-1 bg-red-500 text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
        } else {
            revealBtn.textContent = 'Reveal Answer';
            revealBtn.disabled = false;
            revealBtn.className = 'flex-1 bg-secondary text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
            
            playBtn.disabled = true;
            correctBtn.disabled = true;
            incorrectBtn.disabled = true;
            playBtn.className = 'flex-1 bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
            correctBtn.className = 'flex-1 bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
            incorrectBtn.className = 'flex-1 bg-gray-300 text-gray-600 py-3 rounded-lg cursor-not-allowed';
        }
    }

    revealAnswer() {
        if (!this.currentLetter) return;
        
        this.answersRevealed = true;
        
        // Show the answer
        document.getElementById('letter-name').textContent = this.currentLetter.name;
        document.getElementById('letter-sound').textContent = `Sound: ${this.currentLetter.sound}`;
        
        this.updatePanelButtons();
    }

    markAsCorrect() {
        if (!this.currentLetter || !this.answersRevealed) return;
        
        const previousState = this.letterStates[this.currentLetter.id];
        
        if (previousState !== 'correct') {
            if (previousState === 'incorrect') {
                this.incorrectCount--;
            }
            this.correctCount++;
            this.letterStates[this.currentLetter.id] = 'correct';
        }
        
        this.saveProgress();
        this.renderJumbledGrid();
        this.updateProgress();
        this.hideIdentificationPanel();
        
        this.showNotification('Great! You got it right! 🎉', 'success');
    }

    markAsIncorrect() {
        if (!this.currentLetter || !this.answersRevealed) return;
        
        const previousState = this.letterStates[this.currentLetter.id];
        
        if (previousState !== 'incorrect') {
            if (previousState === 'correct') {
                this.correctCount--;
            }
            this.incorrectCount++;
            this.letterStates[this.currentLetter.id] = 'incorrect';
        }
        
        this.saveProgress();
        this.renderJumbledGrid();
        this.updateProgress();
        this.hideIdentificationPanel();
        
        this.showNotification('Keep practicing! You\'ll get it next time! 💪', 'info');
    }

    hideIdentificationPanel() {
        document.getElementById('identification-panel').classList.add('hidden');
        this.currentLetter = null;
    }

    playLetterSound(letter) {
        if (!letter || !letter.audioFile) {
            this.showNotification('Audio not available for this letter yet', 'warning');
            return;
        }

        const audio = new Audio(letter.audioFile);
        this.showNotification(`Playing ${letter.name} (${letter.letter})`, 'info');
        
        audio.play().catch((error) => {
            console.error('Error playing audio:', error);
            this.showNotification(`Could not play audio for ${letter.name}`, 'warning');
        });
        
        audio.addEventListener('ended', () => {
            this.showNotification(`Finished playing ${letter.name}`, 'success');
        });
    }

    showAllAnswers() {
        // Update all cards to show their names
        const cards = document.querySelectorAll('#jumbled-grid > div');
        cards.forEach((card, index) => {
            const letter = this.jumbledLetters[index];
            const nameDiv = document.createElement('div');
            nameDiv.className = 'text-xs text-secondary mt-1';
            nameDiv.textContent = letter.name;
            
            // Remove existing name if present
            const existingName = card.querySelector('.text-xs.text-secondary');
            if (existingName) {
                existingName.remove();
            }
            
            card.appendChild(nameDiv);
        });
        
        this.showNotification('All answers revealed! 👁️', 'info');
    }

    resetProgress() {
        if (confirm('Are you sure you want to reset your progress for this lesson?')) {
            this.correctCount = 0;
            this.incorrectCount = 0;
            this.letterStates = {};
            
            localStorage.removeItem('lesson-2-progress');
            
            this.renderJumbledGrid();
            this.updateProgress();
            this.hideIdentificationPanel();
            
            this.showNotification('Progress reset. Start practicing again!', 'info');
        }
    }

    updateProgress() {
        const totalAttempted = this.correctCount + this.incorrectCount;
        const percentage = totalAttempted > 0 ? Math.round((this.correctCount / totalAttempted) * 100) : 0;
        
        // Update progress bar
        document.getElementById('lesson-progress').textContent = `${totalAttempted}/28`;
        document.getElementById('lesson-progress-fill').style.width = `${(totalAttempted / 28) * 100}%`;
        
        // Update summary
        document.getElementById('correct-count').textContent = this.correctCount;
        document.getElementById('incorrect-count').textContent = this.incorrectCount;
        document.getElementById('completion-percentage').textContent = `${percentage}%`;
        
        // Enable complete lesson button if significant progress made
        const completeBtn = document.getElementById('complete-lesson');
        if (totalAttempted >= 20 && this.correctCount >= 15) {
            completeBtn.textContent = 'Complete Lesson ✓';
            completeBtn.className = 'bg-primary text-dark px-8 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
        }
    }

    setupEventListeners() {
        // Reveal answer button
        document.getElementById('reveal-answer').addEventListener('click', () => {
            this.revealAnswer();
        });

        // Play sound button
        document.getElementById('play-letter-sound').addEventListener('click', () => {
            if (this.currentLetter) {
                this.playLetterSound(this.currentLetter);
            }
        });

        // Mark correct/incorrect buttons
        document.getElementById('mark-correct').addEventListener('click', () => {
            this.markAsCorrect();
        });

        document.getElementById('mark-incorrect').addEventListener('click', () => {
            this.markAsIncorrect();
        });

        // Control buttons
        document.getElementById('shuffle-letters').addEventListener('click', () => {
            this.shuffleLetters();
            this.renderJumbledGrid();
            this.hideIdentificationPanel();
            this.showNotification('Letters shuffled! 🔀', 'info');
        });

        document.getElementById('show-all-answers').addEventListener('click', () => {
            this.showAllAnswers();
        });

        document.getElementById('reset-progress').addEventListener('click', () => {
            this.resetProgress();
        });

        // Complete lesson button
        document.getElementById('complete-lesson').addEventListener('click', () => {
            this.completeLesson();
        });
    }

    completeLesson() {
        const totalAttempted = this.correctCount + this.incorrectCount;
        
        if (totalAttempted >= 20 && this.correctCount >= 15) {
            // Mark lesson as completed in main app
            const mainAppProgress = JSON.parse(localStorage.getItem('qaidah-progress') || '[]');
            const lesson2 = mainAppProgress.find(l => l.id === 2);
            if (lesson2) {
                lesson2.completed = true;
                localStorage.setItem('qaidah-progress', JSON.stringify(mainAppProgress));
            }
            
            this.showNotification('Congratulations! You have completed Lesson 2!', 'success');
            
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
        } else {
            this.showNotification('Practice more letters to complete the lesson. Try to get at least 15 correct!', 'warning');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${colors[type]}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the lesson when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new JumbledAlphabetLesson();
});
