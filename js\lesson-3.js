// Lesson 3: Letter Positions
class LetterPositionsLesson {
    constructor() {
        this.arabicLetters = [
            {
                id: 1, letter: 'ا', name: 'Alif', sound: 'A - Arm',
                forms: { isolated: 'ا', beginning: 'ا', middle: 'ـا', end: 'ـا' },
                connects: { before: false, after: false },
                examples: [
                    { word: 'أب', position: 'beginning', form: 'أ' },
                    { word: 'قرآن', position: 'middle', form: 'آ' }
                ],
                learned: false
            },
            {
                id: 2, letter: 'ب', name: 'Baa', sound: 'Ba - Ball',
                forms: { isolated: 'ب', beginning: 'بـ', middle: 'ـبـ', end: 'ـب' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'بيت', position: 'beginning', form: 'بـ' },
                    { word: 'كتاب', position: 'end', form: 'ـب' }
                ],
                learned: false
            },
            {
                id: 3, letter: 'ت', name: 'Taa', sound: 'Ta - Task',
                forms: { isolated: 'ت', beginning: 'تـ', middle: 'ـتـ', end: 'ـت' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'تفاح', position: 'beginning', form: 'تـ' },
                    { word: 'بيت', position: 'end', form: 'ـت' }
                ],
                learned: false
            },
            {
                id: 4, letter: 'ث', name: 'Thaa', sound: 'Tha - That',
                forms: { isolated: 'ث', beginning: 'ثـ', middle: 'ـثـ', end: 'ـث' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'ثلاثة', position: 'beginning', form: 'ثـ' },
                    { word: 'حديث', position: 'end', form: 'ـث' }
                ],
                learned: false
            },
            {
                id: 5, letter: 'ج', name: 'Jeem', sound: 'Je - Jet',
                forms: { isolated: 'ج', beginning: 'جـ', middle: 'ـجـ', end: 'ـج' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'جميل', position: 'beginning', form: 'جـ' },
                    { word: 'منج', position: 'end', form: 'ـج' }
                ],
                learned: false
            },
            {
                id: 6, letter: 'ح', name: 'Haa', sound: 'Ha - Hand, Hat',
                forms: { isolated: 'ح', beginning: 'حـ', middle: 'ـحـ', end: 'ـح' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'حمد', position: 'beginning', form: 'حـ' },
                    { word: 'فتح', position: 'end', form: 'ـح' }
                ],
                learned: false
            },
            {
                id: 7, letter: 'خ', name: 'Khaa', sound: 'Kha - Khaki',
                forms: { isolated: 'خ', beginning: 'خـ', middle: 'ـخـ', end: 'ـخ' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'خير', position: 'beginning', form: 'خـ' },
                    { word: 'تاريخ', position: 'end', form: 'ـخ' }
                ],
                learned: false
            },
            {
                id: 8, letter: 'د', name: 'Dal', sound: 'Da - Dab, Dual',
                forms: { isolated: 'د', beginning: 'د', middle: 'ـد', end: 'ـد' },
                connects: { before: false, after: true },
                examples: [
                    { word: 'دار', position: 'beginning', form: 'د' },
                    { word: 'محمد', position: 'end', form: 'ـد' }
                ],
                learned: false
            },
            {
                id: 9, letter: 'ذ', name: 'Thal', sound: 'Zha - Zhan',
                forms: { isolated: 'ذ', beginning: 'ذ', middle: 'ـذ', end: 'ـذ' },
                connects: { before: false, after: true },
                examples: [
                    { word: 'ذهب', position: 'beginning', form: 'ذ' },
                    { word: 'أخذ', position: 'end', form: 'ـذ' }
                ],
                learned: false
            },
            {
                id: 10, letter: 'ر', name: 'Raa', sound: 'Ra - Run',
                forms: { isolated: 'ر', beginning: 'ر', middle: 'ـر', end: 'ـر' },
                connects: { before: false, after: true },
                examples: [
                    { word: 'رجل', position: 'beginning', form: 'ر' },
                    { word: 'نور', position: 'end', form: 'ـر' }
                ],
                learned: false
            },
            {
                id: 11, letter: 'ز', name: 'Zaay', sound: 'Za - Zap',
                forms: { isolated: 'ز', beginning: 'ز', middle: 'ـز', end: 'ـز' },
                connects: { before: false, after: true },
                examples: [
                    { word: 'زهرة', position: 'beginning', form: 'ز' },
                    { word: 'عزيز', position: 'end', form: 'ـز' }
                ],
                learned: false
            },
            {
                id: 12, letter: 'س', name: 'Seen', sound: 'Sa - Sad',
                forms: { isolated: 'س', beginning: 'سـ', middle: 'ـسـ', end: 'ـس' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'سلام', position: 'beginning', form: 'سـ' },
                    { word: 'درس', position: 'end', form: 'ـس' }
                ],
                learned: false
            },
            {
                id: 13, letter: 'ش', name: 'Sheen', sound: 'Sha - Shut',
                forms: { isolated: 'ش', beginning: 'شـ', middle: 'ـشـ', end: 'ـش' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'شمس', position: 'beginning', form: 'شـ' },
                    { word: 'عيش', position: 'end', form: 'ـش' }
                ],
                learned: false
            },
            {
                id: 14, letter: 'ص', name: 'Saad', sound: 'Sa - Saw',
                forms: { isolated: 'ص', beginning: 'صـ', middle: 'ـصـ', end: 'ـص' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'صباح', position: 'beginning', form: 'صـ' },
                    { word: 'خاص', position: 'end', form: 'ـص' }
                ],
                learned: false
            },
            {
                id: 15, letter: 'ض', name: 'Daad', sound: 'Da - Dawn',
                forms: { isolated: 'ض', beginning: 'ضـ', middle: 'ـضـ', end: 'ـض' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'ضوء', position: 'beginning', form: 'ضـ' },
                    { word: 'أرض', position: 'end', form: 'ـض' }
                ],
                learned: false
            },
            {
                id: 16, letter: 'ط', name: 'Taa', sound: 'Ta - Tall',
                forms: { isolated: 'ط', beginning: 'طـ', middle: 'ـطـ', end: 'ـط' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'طعام', position: 'beginning', form: 'طـ' },
                    { word: 'خط', position: 'end', form: 'ـط' }
                ],
                learned: false
            },
            {
                id: 17, letter: 'ظ', name: 'Dhaa', sound: 'Dha - Dhall',
                forms: { isolated: 'ظ', beginning: 'ظـ', middle: 'ـظـ', end: 'ـظ' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'ظهر', position: 'beginning', form: 'ظـ' },
                    { word: 'حفظ', position: 'end', form: 'ـظ' }
                ],
                learned: false
            },
            {
                id: 18, letter: 'ع', name: 'Ayn', sound: 'A - Ayn',
                forms: { isolated: 'ع', beginning: 'عـ', middle: 'ـعـ', end: 'ـع' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'علم', position: 'beginning', form: 'عـ' },
                    { word: 'سمع', position: 'end', form: 'ـع' }
                ],
                learned: false
            },
            {
                id: 19, letter: 'غ', name: 'Ghayn', sound: 'Gha - Ghani',
                forms: { isolated: 'غ', beginning: 'غـ', middle: 'ـغـ', end: 'ـغ' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'غروب', position: 'beginning', form: 'غـ' },
                    { word: 'بلغ', position: 'end', form: 'ـغ' }
                ],
                learned: false
            },
            {
                id: 20, letter: 'ف', name: 'Faa', sound: 'Fa - Fan',
                forms: { isolated: 'ف', beginning: 'فـ', middle: 'ـفـ', end: 'ـف' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'فيل', position: 'beginning', form: 'فـ' },
                    { word: 'كيف', position: 'end', form: 'ـف' }
                ],
                learned: false
            },
            {
                id: 21, letter: 'ق', name: 'Qaaf', sound: 'Qa - Qalam',
                forms: { isolated: 'ق', beginning: 'قـ', middle: 'ـقـ', end: 'ـق' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'قرآن', position: 'beginning', form: 'قـ' },
                    { word: 'حق', position: 'end', form: 'ـق' }
                ],
                learned: false
            },
            {
                id: 22, letter: 'ك', name: 'Kaaf', sound: 'Ka - Kalam',
                forms: { isolated: 'ك', beginning: 'كـ', middle: 'ـكـ', end: 'ـك' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'كتاب', position: 'beginning', form: 'كـ' },
                    { word: 'ملك', position: 'end', form: 'ـك' }
                ],
                learned: false
            },
            {
                id: 23, letter: 'ل', name: 'Laam', sound: 'La - Lamp',
                forms: { isolated: 'ل', beginning: 'لـ', middle: 'ـلـ', end: 'ـل' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'لعب', position: 'beginning', form: 'لـ' },
                    { word: 'جمل', position: 'end', form: 'ـل' }
                ],
                learned: false
            },
            {
                id: 24, letter: 'م', name: 'Meem', sound: 'Ma - Man',
                forms: { isolated: 'م', beginning: 'مـ', middle: 'ـمـ', end: 'ـم' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'مدرسة', position: 'beginning', form: 'مـ' },
                    { word: 'اسم', position: 'end', form: 'ـم' }
                ],
                learned: false
            },
            {
                id: 25, letter: 'ن', name: 'Noon', sound: 'Na - Nut',
                forms: { isolated: 'ن', beginning: 'نـ', middle: 'ـنـ', end: 'ـن' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'نور', position: 'beginning', form: 'نـ' },
                    { word: 'لسان', position: 'end', form: 'ـن' }
                ],
                learned: false
            },
            {
                id: 26, letter: 'ه', name: 'Haa', sound: 'Ha - Hat',
                forms: { isolated: 'ه', beginning: 'هـ', middle: 'ـهـ', end: 'ـه' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'هدية', position: 'beginning', form: 'هـ' },
                    { word: 'الله', position: 'end', form: 'ـه' }
                ],
                learned: false
            },
            {
                id: 27, letter: 'و', name: 'Waaw', sound: 'Wa - Wall',
                forms: { isolated: 'و', beginning: 'و', middle: 'ـو', end: 'ـو' },
                connects: { before: false, after: true },
                examples: [
                    { word: 'ولد', position: 'beginning', form: 'و' },
                    { word: 'نحو', position: 'end', form: 'ـو' }
                ],
                learned: false
            },
            {
                id: 28, letter: 'ي', name: 'Yaa', sound: 'Ya - Yes',
                forms: { isolated: 'ي', beginning: 'يـ', middle: 'ـيـ', end: 'ـي' },
                connects: { before: true, after: true },
                examples: [
                    { word: 'يوم', position: 'beginning', form: 'يـ' },
                    { word: 'في', position: 'end', form: 'ـي' }
                ],
                learned: false
            }
        ];

        this.currentMode = 'overview';
        this.currentLetter = null;
        this.learnedCount = 0;
        this.quizScore = 0;
        this.quizTotal = 0;

        this.init();
    }

    init() {
        this.loadProgress();
        this.setupEventListeners();
        this.renderOverviewMode();
        this.updateProgress();
    }

    loadProgress() {
        const saved = localStorage.getItem('lesson-3-progress');
        if (saved) {
            const progress = JSON.parse(saved);
            progress.forEach(savedLetter => {
                const letter = this.arabicLetters.find(l => l.id === savedLetter.id);
                if (letter) {
                    letter.learned = savedLetter.learned;
                }
            });
        }
        this.learnedCount = this.arabicLetters.filter(l => l.learned).length;
    }

    saveProgress() {
        const progress = this.arabicLetters.map(letter => ({
            id: letter.id,
            learned: letter.learned
        }));
        localStorage.setItem('lesson-3-progress', JSON.stringify(progress));
    }

    setupEventListeners() {
        // Mode switching
        document.getElementById('overview-mode').addEventListener('click', () => this.switchMode('overview'));
        document.getElementById('practice-mode').addEventListener('click', () => this.switchMode('practice'));
        document.getElementById('quiz-mode').addEventListener('click', () => this.switchMode('quiz'));

        // Complete lesson button
        document.getElementById('complete-lesson').addEventListener('click', () => this.completeLesson());
    }

    switchMode(mode) {
        this.currentMode = mode;
        
        // Update button styles
        document.querySelectorAll('[id$="-mode"]').forEach(btn => {
            btn.className = 'bg-gray-200 text-dark py-3 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors';
        });
        document.getElementById(`${mode}-mode`).className = 'bg-primary text-dark py-3 px-4 rounded-lg font-semibold hover:bg-opacity-90 transition-colors';

        // Show/hide content
        document.getElementById('overview-content').classList.toggle('hidden', mode !== 'overview');
        document.getElementById('practice-content').classList.toggle('hidden', mode !== 'practice');
        document.getElementById('quiz-content').classList.toggle('hidden', mode !== 'quiz');

        // Render content based on mode
        switch(mode) {
            case 'overview':
                this.renderOverviewMode();
                break;
            case 'practice':
                this.renderPracticeMode();
                break;
            case 'quiz':
                this.renderQuizMode();
                break;
        }
    }

    renderOverviewMode() {
        const grid = document.getElementById('letters-shapes-grid');
        grid.innerHTML = '';

        this.arabicLetters.forEach(letter => {
            const row = document.createElement('div');
            row.className = `grid grid-cols-5 gap-2 p-2 rounded-lg hover:bg-light transition-colors cursor-pointer ${letter.learned ? 'bg-green-50 border border-green-200' : 'bg-white border border-gray-200'}`;
            
            row.innerHTML = `
                <div class="text-center p-3 font-arabic text-4xl text-primary font-bold">${letter.letter}</div>
                <div class="text-center p-3 font-arabic text-3xl text-secondary font-bold">${letter.forms.end}</div>
                <div class="text-center p-3 font-arabic text-3xl text-secondary font-bold">${letter.forms.middle}</div>
                <div class="text-center p-3 font-arabic text-3xl text-secondary font-bold">${letter.forms.beginning}</div>
                <div class="text-center p-3 font-arabic text-3xl text-secondary font-bold">${letter.forms.isolated}</div>
            `;

            row.addEventListener('click', () => this.selectLetterForPractice(letter));
            grid.appendChild(row);
        });
    }

    selectLetterForPractice(letter) {
        this.currentLetter = letter;
        this.switchMode('practice');
    }

    renderPracticeMode() {
        this.renderLetterSelector();
        if (this.currentLetter) {
            this.showLetterDetail(this.currentLetter);
        }
    }

    renderLetterSelector() {
        const selector = document.getElementById('letter-selector');
        selector.innerHTML = '';

        this.arabicLetters.forEach(letter => {
            const btn = document.createElement('button');
            btn.className = `p-4 rounded-lg font-arabic text-3xl font-bold transition-colors ${
                letter.learned ? 'bg-green-100 text-green-800 border border-green-300' :
                this.currentLetter && this.currentLetter.id === letter.id ? 'bg-primary text-dark' :
                'bg-white text-secondary border border-gray-200 hover:bg-gray-50'
            }`;
            btn.textContent = letter.letter;
            btn.addEventListener('click', () => {
                this.currentLetter = letter;
                this.showLetterDetail(letter);
                this.renderLetterSelector(); // Re-render to update selection
            });
            selector.appendChild(btn);
        });
    }

    showLetterDetail(letter) {
        const detail = document.getElementById('letter-detail');
        detail.classList.remove('hidden');
        
        detail.innerHTML = `
            <div class="text-center mb-8">
                <div class="text-8xl font-arabic text-primary font-bold mb-4">${letter.letter}</div>
                <h3 class="text-2xl font-bold text-dark mb-2">${letter.name}</h3>
                <p class="text-lg text-secondary mb-6">Sound: ${letter.sound}</p>
            </div>

            <div class="grid md:grid-cols-2 gap-8 mb-8">
                <div>
                    <h4 class="text-xl font-bold text-dark mb-4">Positional Forms</h4>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-4 bg-light rounded-lg">
                            <span class="font-semibold">Isolated (Alone):</span>
                            <span class="text-4xl font-arabic text-primary font-bold">${letter.forms.isolated}</span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-light rounded-lg">
                            <span class="font-semibold">Beginning:</span>
                            <span class="text-4xl font-arabic text-primary font-bold">${letter.forms.beginning}</span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-light rounded-lg">
                            <span class="font-semibold">Middle:</span>
                            <span class="text-4xl font-arabic text-primary font-bold">${letter.forms.middle}</span>
                        </div>
                        <div class="flex justify-between items-center p-4 bg-light rounded-lg">
                            <span class="font-semibold">End:</span>
                            <span class="text-4xl font-arabic text-primary font-bold">${letter.forms.end}</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-xl font-bold text-dark mb-4">Examples in Words</h4>
                    <div class="space-y-4">
                        ${letter.examples.map(example => `
                            <div class="p-4 bg-light rounded-lg">
                                <div class="text-3xl font-arabic text-primary font-bold mb-2">${example.word}</div>
                                <div class="text-sm text-secondary">
                                    <span class="font-semibold">${example.position}</span> position:
                                    <span class="font-arabic text-2xl text-primary font-bold">${example.form}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>

            <div class="text-center space-y-4">
                <button id="mark-learned-${letter.id}" class="w-full bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold ${letter.learned ? 'opacity-50 cursor-not-allowed' : ''}" ${letter.learned ? 'disabled' : ''}>
                    ${letter.learned ? '✓ Already Learned' : '✓ Mark as Learned'}
                </button>
                
                <div class="flex space-x-3">
                    <button id="prev-letter" class="flex-1 px-4 py-2 border border-secondary rounded-lg hover:bg-gray-50 transition-colors">
                        ← Previous
                    </button>
                    <button id="next-letter" class="flex-1 px-4 py-2 border border-secondary rounded-lg hover:bg-gray-50 transition-colors">
                        Next →
                    </button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        if (!letter.learned) {
            document.getElementById(`mark-learned-${letter.id}`).addEventListener('click', () => {
                this.markLetterAsLearned(letter);
            });
        }

        document.getElementById('prev-letter').addEventListener('click', () => this.goToPreviousLetter());
        document.getElementById('next-letter').addEventListener('click', () => this.goToNextLetter());
    }

    markLetterAsLearned(letter) {
        letter.learned = true;
        this.learnedCount++;
        this.saveProgress();
        this.updateProgress();
        this.showLetterDetail(letter);
        this.renderLetterSelector();
        this.showNotification(`Great! You've learned ${letter.name} positions!`, 'success');
    }

    goToNextLetter() {
        const currentIndex = this.arabicLetters.findIndex(l => l.id === this.currentLetter.id);
        const nextIndex = (currentIndex + 1) % this.arabicLetters.length;
        this.currentLetter = this.arabicLetters[nextIndex];
        this.showLetterDetail(this.currentLetter);
        this.renderLetterSelector();
    }

    goToPreviousLetter() {
        const currentIndex = this.arabicLetters.findIndex(l => l.id === this.currentLetter.id);
        const prevIndex = currentIndex === 0 ? this.arabicLetters.length - 1 : currentIndex - 1;
        this.currentLetter = this.arabicLetters[prevIndex];
        this.showLetterDetail(this.currentLetter);
        this.renderLetterSelector();
    }

    renderQuizMode() {
        const container = document.getElementById('quiz-container');

        if (this.learnedCount < 5) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <h4 class="text-xl font-bold text-dark mb-4">Learn More Letters First!</h4>
                    <p class="text-secondary">You need to learn at least 5 letters before taking the quiz.</p>
                    <p class="text-secondary">Current progress: ${this.learnedCount}/28 letters learned</p>
                </div>
            `;
            return;
        }

        this.generateQuiz();
    }

    generateQuiz() {
        const learnedLetters = this.arabicLetters.filter(l => l.learned);
        const quizQuestions = [];

        // Generate 10 random questions from learned letters
        for (let i = 0; i < Math.min(10, learnedLetters.length * 2); i++) {
            const letter = learnedLetters[Math.floor(Math.random() * learnedLetters.length)];
            const positions = ['isolated', 'beginning', 'middle', 'end'];
            const correctPosition = positions[Math.floor(Math.random() * positions.length)];

            // Create wrong options from other forms of the same letter
            const wrongOptions = positions
                .filter(p => p !== correctPosition)
                .map(p => letter.forms[p])
                .slice(0, 3);

            const options = [letter.forms[correctPosition], ...wrongOptions]
                .sort(() => Math.random() - 0.5);

            quizQuestions.push({
                id: i + 1,
                letter: letter.letter,
                letterName: letter.name,
                position: correctPosition,
                correctAnswer: letter.forms[correctPosition],
                options: options,
                answered: false,
                correct: false
            });
        }

        this.currentQuiz = quizQuestions;
        this.currentQuestionIndex = 0;
        this.quizScore = 0;
        this.renderQuizQuestion();
    }

    renderQuizQuestion() {
        const container = document.getElementById('quiz-container');
        const question = this.currentQuiz[this.currentQuestionIndex];

        container.innerHTML = `
            <div class="text-center mb-6">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-secondary">Question ${this.currentQuestionIndex + 1} of ${this.currentQuiz.length}</span>
                    <span class="text-sm text-secondary">Score: ${this.quizScore}/${this.currentQuestionIndex}</span>
                </div>
                <div class="bg-gray-200 rounded-full h-2 mb-4">
                    <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: ${((this.currentQuestionIndex) / this.currentQuiz.length) * 100}%"></div>
                </div>
            </div>

            <div class="text-center mb-8">
                <h4 class="text-xl font-bold text-dark mb-4">Which form of "${question.letterName}" (<span class="font-arabic text-3xl font-bold text-primary">${question.letter}</span>) is used in the <strong>${question.position}</strong> position?</h4>

                <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
                    ${question.options.map((option, index) => `
                        <button class="quiz-option p-6 border-2 border-gray-200 rounded-lg hover:border-primary transition-colors text-4xl font-arabic font-bold" data-answer="${option}">
                            ${option}
                        </button>
                    `).join('')}
                </div>
            </div>

            <div id="quiz-feedback" class="hidden text-center mb-6">
                <!-- Feedback will be shown here -->
            </div>

            <div class="text-center">
                <button id="next-question" class="bg-primary text-dark px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold hidden">
                    ${this.currentQuestionIndex === this.currentQuiz.length - 1 ? 'Finish Quiz' : 'Next Question'}
                </button>
            </div>
        `;

        // Add event listeners for quiz options
        document.querySelectorAll('.quiz-option').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuizAnswer(e.target.dataset.answer, question));
        });

        document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
    }

    handleQuizAnswer(selectedAnswer, question) {
        const isCorrect = selectedAnswer === question.correctAnswer;
        question.answered = true;
        question.correct = isCorrect;

        if (isCorrect) {
            this.quizScore++;
        }

        // Update button styles
        document.querySelectorAll('.quiz-option').forEach(btn => {
            btn.disabled = true;
            if (btn.dataset.answer === question.correctAnswer) {
                btn.className += ' bg-green-100 border-green-500 text-green-800';
            } else if (btn.dataset.answer === selectedAnswer && !isCorrect) {
                btn.className += ' bg-red-100 border-red-500 text-red-800';
            } else {
                btn.className += ' opacity-50';
            }
        });

        // Show feedback
        const feedback = document.getElementById('quiz-feedback');
        feedback.classList.remove('hidden');
        feedback.innerHTML = `
            <div class="p-4 rounded-lg ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                <div class="text-lg font-bold mb-2">${isCorrect ? '✓ Correct!' : '✗ Incorrect'}</div>
                <div>The ${question.position} form of ${question.letterName} is: <span class="font-arabic text-3xl font-bold">${question.correctAnswer}</span></div>
            </div>
        `;

        // Show next button
        document.getElementById('next-question').classList.remove('hidden');
    }

    nextQuestion() {
        if (this.currentQuestionIndex < this.currentQuiz.length - 1) {
            this.currentQuestionIndex++;
            this.renderQuizQuestion();
        } else {
            this.showQuizResults();
        }
    }

    showQuizResults() {
        const container = document.getElementById('quiz-container');
        const percentage = Math.round((this.quizScore / this.currentQuiz.length) * 100);

        let resultMessage = '';
        let resultClass = '';

        if (percentage >= 80) {
            resultMessage = 'Excellent! You have mastered letter forms!';
            resultClass = 'text-green-800 bg-green-100';
        } else if (percentage >= 60) {
            resultMessage = 'Good job! Keep practicing to improve.';
            resultClass = 'text-blue-800 bg-blue-100';
        } else {
            resultMessage = 'Keep practicing! Review the letter forms and try again.';
            resultClass = 'text-red-800 bg-red-100';
        }

        container.innerHTML = `
            <div class="text-center">
                <div class="text-6xl mb-4">${percentage >= 80 ? '🏆' : percentage >= 60 ? '👍' : '📚'}</div>
                <h3 class="text-2xl font-bold text-dark mb-4">Quiz Complete!</h3>

                <div class="max-w-md mx-auto mb-6">
                    <div class="text-4xl font-bold text-primary mb-2">${this.quizScore}/${this.currentQuiz.length}</div>
                    <div class="text-xl text-secondary mb-4">${percentage}% Correct</div>

                    <div class="p-4 rounded-lg ${resultClass}">
                        ${resultMessage}
                    </div>
                </div>

                <div class="space-y-3">
                    <button id="retake-quiz" class="w-full bg-primary text-dark py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                        Retake Quiz
                    </button>
                    <button id="back-to-practice" class="w-full bg-gray-200 text-dark py-3 rounded-lg hover:bg-gray-300 transition-colors font-semibold">
                        Back to Practice
                    </button>
                </div>
            </div>
        `;

        document.getElementById('retake-quiz').addEventListener('click', () => this.generateQuiz());
        document.getElementById('back-to-practice').addEventListener('click', () => this.switchMode('practice'));
    }

    updateProgress() {
        const totalLetters = this.arabicLetters.length;
        const percentage = Math.round((this.learnedCount / totalLetters) * 100);
        
        document.getElementById('lesson-progress').textContent = `${this.learnedCount}/${totalLetters}`;
        document.getElementById('lesson-progress-fill').style.width = `${percentage}%`;
        document.getElementById('letters-learned').textContent = this.learnedCount;
        document.getElementById('completion-percentage').textContent = `${percentage}%`;
        
        const completeBtn = document.getElementById('complete-lesson');
        if (this.learnedCount === totalLetters) {
            completeBtn.textContent = 'Complete Lesson ✓';
            completeBtn.className = 'bg-primary text-dark px-8 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold';
        }
    }

    completeLesson() {
        if (this.learnedCount === this.arabicLetters.length) {
            const mainAppProgress = JSON.parse(localStorage.getItem('qaidah-progress') || '[]');
            const lesson3 = mainAppProgress.find(l => l.id === 3);
            if (lesson3) {
                lesson3.completed = true;
                localStorage.setItem('qaidah-progress', JSON.stringify(mainAppProgress));
            }

            this.showNotification('Congratulations! You have completed Lesson 3!', 'success');

            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
        } else {
            this.showNotification('Please learn all letter positions before completing the lesson.', 'warning');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${colors[type]}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the lesson when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LetterPositionsLesson();
});
