// Audio player functionality for Quran recitation
class AudioPlayer {
    constructor() {
        this.currentAudio = null;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        this.volume = 1.0;
        
        this.init();
    }

    init() {
        this.setupAudioControls();
        this.setupVerseClickHandlers();
    }

    setupAudioControls() {
        // Create audio control elements if they don't exist
        this.createAudioControls();
    }

    createAudioControls() {
        // This would be called when a lesson with audio is opened
        const audioControlsHTML = `
            <div class="audio-controls mt-6">
                <button id="play-pause-btn" class="play-button">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                
                <div class="flex-1 mx-4">
                    <div class="progress-bar">
                        <div id="audio-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600 mt-1">
                        <span id="current-time">0:00</span>
                        <span id="total-time">0:00</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824zM15 8a2 2 0 012 2v0a2 2 0 01-2 2" clip-rule="evenodd"></path>
                    </svg>
                    <input type="range" id="volume-slider" min="0" max="100" value="100" class="w-20">
                </div>
            </div>
        `;
        
        return audioControlsHTML;
    }

    setupVerseClickHandlers() {
        // Add click handlers to Arabic text for pronunciation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('arabic-text') || e.target.closest('.arabic-text')) {
                this.playVerseAudio(e.target);
            }
        });
    }

    playVerseAudio(element) {
        // Highlight the clicked verse
        this.highlightVerse(element);
        
        // In a real implementation, you would have actual audio files
        // For now, we'll simulate audio playback
        this.simulateAudioPlayback(element);
    }

    highlightVerse(element) {
        // Remove previous highlights
        document.querySelectorAll('.verse-highlight').forEach(el => {
            el.classList.remove('verse-highlight');
        });
        
        // Add highlight to current verse
        element.classList.add('verse-highlight');
        
        // Remove highlight after a delay
        setTimeout(() => {
            element.classList.remove('verse-highlight');
        }, 3000);
    }

    simulateAudioPlayback(element) {
        // This simulates audio playback
        // In a real app, you would load and play actual audio files
        
        console.log('Playing audio for:', element.textContent);
        
        // Show a notification that audio is playing
        this.showAudioNotification('Playing pronunciation...');

        // Simulate audio duration
        setTimeout(() => {
            this.showAudioNotification('Audio completed', 'success');
        }, 2000);
    }

    loadAudio(audioUrl) {
        if (this.currentAudio) {
            this.currentAudio.pause();
        }
        
        this.currentAudio = new Audio(audioUrl);
        this.setupAudioEventListeners();
        
        return new Promise((resolve, reject) => {
            this.currentAudio.addEventListener('loadedmetadata', () => {
                this.duration = this.currentAudio.duration;
                this.updateTimeDisplay();
                resolve();
            });
            
            this.currentAudio.addEventListener('error', reject);
        });
    }

    setupAudioEventListeners() {
        if (!this.currentAudio) return;
        
        this.currentAudio.addEventListener('timeupdate', () => {
            this.currentTime = this.currentAudio.currentTime;
            this.updateProgress();
            this.updateTimeDisplay();
        });
        
        this.currentAudio.addEventListener('ended', () => {
            this.isPlaying = false;
            this.updatePlayButton();
        });
        
        this.currentAudio.addEventListener('play', () => {
            this.isPlaying = true;
            this.updatePlayButton();
        });
        
        this.currentAudio.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton();
        });
    }

    play() {
        if (this.currentAudio && !this.isPlaying) {
            this.currentAudio.play();
        }
    }

    pause() {
        if (this.currentAudio && this.isPlaying) {
            this.currentAudio.pause();
        }
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    setVolume(volume) {
        this.volume = volume / 100;
        if (this.currentAudio) {
            this.currentAudio.volume = this.volume;
        }
    }

    seek(percentage) {
        if (this.currentAudio && this.duration) {
            const newTime = (percentage / 100) * this.duration;
            this.currentAudio.currentTime = newTime;
        }
    }

    updateProgress() {
        if (this.duration > 0) {
            const percentage = (this.currentTime / this.duration) * 100;
            const progressBar = document.getElementById('audio-progress');
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }
        }
    }

    updateTimeDisplay() {
        const currentTimeEl = document.getElementById('current-time');
        const totalTimeEl = document.getElementById('total-time');
        
        if (currentTimeEl) {
            currentTimeEl.textContent = this.formatTime(this.currentTime);
        }
        
        if (totalTimeEl) {
            totalTimeEl.textContent = this.formatTime(this.duration);
        }
    }

    updatePlayButton() {
        const playBtn = document.getElementById('play-pause-btn');
        if (!playBtn) return;
        
        const icon = this.isPlaying ? 
            `<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>` :
            `<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
            </svg>`;
        
        playBtn.innerHTML = icon;
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    showAudioNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed bottom-4 right-4 p-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'}`;
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824z" clip-rule="evenodd"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 2000);
    }

    // Method to add audio controls to a lesson modal
    addAudioControlsToLesson(lessonContainer) {
        const audioControlsHTML = this.createAudioControls();
        lessonContainer.insertAdjacentHTML('beforeend', audioControlsHTML);
        
        // Setup event listeners for the new controls
        const playBtn = lessonContainer.querySelector('#play-pause-btn');
        const volumeSlider = lessonContainer.querySelector('#volume-slider');
        const progressBar = lessonContainer.querySelector('.progress-bar');
        
        if (playBtn) {
            playBtn.addEventListener('click', () => this.togglePlayPause());
        }
        
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                this.setVolume(e.target.value);
            });
        }
        
        if (progressBar) {
            progressBar.addEventListener('click', (e) => {
                const rect = progressBar.getBoundingClientRect();
                const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                this.seek(percentage);
            });
        }
    }
}

// Initialize audio player
const audioPlayer = new AudioPlayer();
